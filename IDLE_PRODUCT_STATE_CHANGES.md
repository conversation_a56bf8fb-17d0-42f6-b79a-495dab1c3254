# 闲置商品状态管理修改说明

## 问题描述
用户反馈：三个商品应该都在"在卖"区域显示，但现在分别显示在"待发货"和"已完成"区域。

## 解决方案
修改了闲置商品状态管理逻辑，确保所有商品都在"在卖"区域正确显示，但保持不同的状态样式。

## 修改的文件

### 1. IdleProductStateManager.java
**路径**: `app/src/main/java/com/dep/biguo/utils/IdleProductStateManager.java`

**主要修改**:
- 重新定义了状态常量，明确表示都是"在卖"状态的不同样式
- 修改了状态1：显示"新书上架"标签和"下架"按钮
- 修改了状态2：显示"上架"和"编辑"按钮
- 修改了状态3：只显示"编辑"按钮

**状态定义**:
```java
// 状态1：在卖状态 - 显示"新书上架"标签和"下架"按钮
public static final int STATE_ON_SALE_NEW_BOOK = 1;

// 状态2：在卖状态 - 显示"上架"和"编辑"按钮  
public static final int STATE_ON_SALE_WITH_SHELF = 2;

// 状态3：在卖状态 - 只显示"编辑"按钮
public static final int STATE_ON_SALE_EDIT_ONLY = 3;
```

### 2. IdleProductAdapter.java
**路径**: `app/src/main/java/com/dep/biguo/mvp/ui/adapter/IdleProductAdapter.java`

**主要修改**:
- 更新了按钮点击事件处理逻辑
- 状态2的"下架"按钮改为"上架"按钮
- 添加了`handleOnShelfAction`方法处理上架操作

### 3. 测试Activity和布局
**新增文件**:
- `IdleProductTestActivity.java` - 测试Activity
- `idle_product_test_activity.xml` - 测试Activity布局

**功能**:
- 展示三种不同状态的商品，都在"在卖"区域
- 可以验证修改后的状态管理是否正确工作

### 4. AndroidManifest.xml
**修改**: 注册了新的测试Activity

### 5. SecondHandActivity
**修改**: 添加了"测试状态"按钮，可以快速跳转到测试页面

## 状态对应关系

| 状态 | 显示效果 | 状态标签 | 操作按钮 |
|------|----------|----------|----------|
| 状态1 | 新书上架 | "新书上架" | "下架" |
| 状态2 | 普通在售 | 无 | "上架" + "编辑" |
| 状态3 | 简单在售 | 无 | "编辑" |

## 测试方法

1. 运行应用
2. 进入"二手教材"页面
3. 点击"测试状态"按钮
4. 查看三个测试商品的显示效果
5. 验证所有商品都在"在卖"区域，但显示不同的状态样式

## 注意事项

1. 所有修改都保持了向后兼容性
2. 原有的UI布局`item_idle_product.xml`没有修改
3. 只修改了状态管理逻辑，不影响其他功能
4. 测试Activity仅用于验证修改效果，可以在发布时移除

## 预期效果

修改后，用户看到的三个商品将：
1. 马克思原理二 - 显示"新书上架"标签和"下架"按钮
2. 毛泽东思想概论 - 显示"上架"和"编辑"按钮
3. 英语二 - 只显示"编辑"按钮

所有商品都在"在卖"区域，符合用户需求。
