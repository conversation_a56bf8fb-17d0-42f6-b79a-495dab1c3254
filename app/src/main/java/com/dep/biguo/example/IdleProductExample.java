package com.dep.biguo.example;

import com.dep.biguo.bean.OrderBean;
import com.dep.biguo.utils.IdleProductStateManager;
import java.util.ArrayList;
import java.util.List;

/**
 * 闲置商品状态示例代码
 * 演示如何通过Java代码控制三种不同的状态显示
 */
public class IdleProductExample {

    /**
     * 创建三种不同状态的测试数据 - 都在"在卖"区域
     * @return 包含三种状态的商品列表
     */
    public static List<OrderBean> createTestData() {
        List<OrderBean> testData = new ArrayList<>();

        // 状态1：在卖状态 - 显示"新书上架"标签和"下架"按钮
        OrderBean item1 = IdleProductStateManager.createTestData(
            "马克思原理二",
            "16.00",
            IdleProductStateManager.STATE_ON_SALE_NEW_BOOK
        );
        testData.add(item1);

        // 状态2：在卖状态 - 显示"上架"和"编辑"按钮
        OrderBean item2 = IdleProductStateManager.createTestData(
            "毛泽东思想和中国特色社会主义理论体系概论",
            "30.00",
            IdleProductStateManager.STATE_ON_SALE_WITH_SHELF
        );
        testData.add(item2);

        // 状态3：在卖状态 - 只显示"编辑"按钮
        OrderBean item3 = IdleProductStateManager.createTestData(
            "英语二",
            "30.00",
            IdleProductStateManager.STATE_ON_SALE_EDIT_ONLY
        );
        testData.add(item3);

        return testData;
    }

    /**
     * 在Activity中使用示例
     * 
     * 使用方法：
     * 
     * // 1. 在Activity中获取测试数据
     * List<OrderBean> testData = IdleProductExample.createTestData();
     * 
     * // 2. 设置到适配器
     * IdleProductAdapter adapter = new IdleProductAdapter(testData);
     * recyclerView.setAdapter(adapter);
     * 
     * // 3. 动态改变状态
     * OrderBean item = testData.get(0);
     * item.setState(IdleProductStateManager.STATE_ON_SALE_WITH_SHELF);
     * adapter.notifyItemChanged(0);
     */
    
    /**
     * 动态改变商品状态的示例方法
     * @param adapter 适配器
     * @param position 位置
     * @param newState 新状态
     */
    public static void changeItemState(
        com.dep.biguo.mvp.ui.adapter.IdleProductAdapter adapter, 
        int position, 
        int newState
    ) {
        if (adapter != null && adapter.getData() != null && 
            position >= 0 && position < adapter.getData().size()) {
            
            OrderBean item = adapter.getData().get(position);
            item.setState(newState);
            adapter.notifyItemChanged(position);
        }
    }

    /**
     * 批量设置状态的示例方法
     * @param adapter 适配器
     * @param states 状态数组，与数据位置对应
     */
    public static void batchSetStates(
        com.dep.biguo.mvp.ui.adapter.IdleProductAdapter adapter, 
        int[] states
    ) {
        if (adapter != null && adapter.getData() != null && states != null) {
            List<OrderBean> data = adapter.getData();
            int minLength = Math.min(data.size(), states.length);
            
            for (int i = 0; i < minLength; i++) {
                data.get(i).setState(states[i]);
            }
            
            adapter.notifyDataSetChanged();
        }
    }
}
