package com.dep.biguo.utils;

import android.view.View;
import android.widget.TextView;
import com.chad.library.adapter.base.BaseViewHolder;
import com.dep.biguo.R;
import com.dep.biguo.bean.OrderBean;
import com.dep.biguo.bean.ShopBean;
import java.util.ArrayList;
import java.util.List;

/**
 * 闲置商品状态管理工具类
 * 用于控制不同状态下的UI显示
 */
public class IdleProductStateManager {

    /**
     * 状态1：在卖状态 - 显示"新书上架"标签和"下架"按钮
     */
    public static final int STATE_ON_SALE_NEW_BOOK = 1;

    /**
     * 状态2：在卖状态 - 显示"上架"和"编辑"按钮
     */
    public static final int STATE_ON_SALE_WITH_SHELF = 2;

    /**
     * 状态3：在卖状态 - 只显示"编辑"按钮
     */
    public static final int STATE_ON_SALE_EDIT_ONLY = 3;

    /**
     * 根据状态设置UI显示
     * @param helper BaseViewHolder
     * @param item OrderBean数据
     */
    public static void setupUIByState(BaseViewHolder helper, OrderBean item) {
        TextView tvStatusTag = helper.getView(R.id.tvStatusTag);
        TextView btnAction = helper.getView(R.id.btnAction);
        TextView btnAction2 = helper.getView(R.id.btnAction2);

        int state = item.getState();
        
        switch (state) {
            case STATE_ON_SALE_NEW_BOOK:
                // 状态1：在卖状态 - 显示"新书上架"标签和"下架"按钮
                setupState1(tvStatusTag, btnAction, btnAction2);
                break;

            case STATE_ON_SALE_WITH_SHELF:
                // 状态2：在卖状态 - 显示"上架"和"编辑"按钮
                setupState2(tvStatusTag, btnAction, btnAction2);
                break;

            case STATE_ON_SALE_EDIT_ONLY:
                // 状态3：在卖状态 - 只显示"编辑"按钮
                setupState3(tvStatusTag, btnAction, btnAction2);
                break;
                
            default:
                // 默认状态：显示所有控件
                setupDefaultState(tvStatusTag, btnAction, btnAction2);
                break;
        }
    }

    /**
     * 状态1：在卖状态 - 显示"新书上架"标签和"下架"按钮
     */
    private static void setupState1(TextView tvStatusTag, TextView btnAction, TextView btnAction2) {
        // 显示状态标签 - 在卖区域
        tvStatusTag.setText("新书上架");
        tvStatusTag.setVisibility(View.VISIBLE);

        // 主按钮显示"下架"
        btnAction.setText("下架");
        btnAction.setVisibility(View.VISIBLE);

        // 隐藏第二个按钮
        btnAction2.setVisibility(View.GONE);
    }

    /**
     * 状态2：在卖状态 - 显示"上架"和"编辑"按钮
     */
    private static void setupState2(TextView tvStatusTag, TextView btnAction, TextView btnAction2) {
        // 隐藏状态标签 - 在卖区域
        tvStatusTag.setVisibility(View.GONE);

        // 第二个按钮显示"上架"
        btnAction2.setText("上架");
        btnAction2.setVisibility(View.VISIBLE);

        // 主按钮显示"编辑"
        btnAction.setText("编辑");
        btnAction.setVisibility(View.VISIBLE);
    }

    /**
     * 状态3：在卖状态 - 只显示"编辑"按钮
     */
    private static void setupState3(TextView tvStatusTag, TextView btnAction, TextView btnAction2) {
        // 隐藏状态标签 - 在卖区域
        tvStatusTag.setVisibility(View.GONE);

        // 主按钮显示"编辑"
        btnAction.setText("编辑");
        btnAction.setVisibility(View.VISIBLE);

        // 隐藏第二个按钮
        btnAction2.setVisibility(View.GONE);
    }

    /**
     * 默认状态：显示所有控件
     */
    private static void setupDefaultState(TextView tvStatusTag, TextView btnAction, TextView btnAction2) {
        // 显示状态标签
        tvStatusTag.setText("在售中");
        tvStatusTag.setVisibility(View.VISIBLE);
        
        // 主按钮显示"编辑"
        btnAction.setText("编辑");
        btnAction.setVisibility(View.VISIBLE);
        
        // 第二个按钮显示"下架"
        btnAction2.setText("下架");
        btnAction2.setVisibility(View.VISIBLE);
    }

    /**
     * 创建测试数据
     * @param name 商品名称
     * @param price 价格
     * @param state 状态
     * @return OrderBean
     */
    public static OrderBean createTestData(String name, String price, int state) {
        OrderBean item = new OrderBean();
        item.setOrder_id(1000 + state);
        item.setName(name);
        item.setType("idle");
        item.setState(state);
        item.setTotal_fee(price);

        // 创建商品数据
        java.util.List<com.dep.biguo.bean.ShopBean> goodsData = new java.util.ArrayList<>();
        com.dep.biguo.bean.ShopBean shopBean = new com.dep.biguo.bean.ShopBean();
        shopBean.setName(name);
        shopBean.setPrice(price);
        shopBean.setImg(""); // 使用默认图片
        goodsData.add(shopBean);
        item.setGoods_data(goodsData);

        return item;
    }
}
