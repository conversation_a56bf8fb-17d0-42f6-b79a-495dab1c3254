package com.dep.biguo.mvp.ui.activity;

import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.dep.biguo.R;
import com.dep.biguo.bean.OrderBean;
import com.dep.biguo.databinding.IdleProductTestActivityBinding;
import com.dep.biguo.mvp.ui.adapter.IdleProductAdapter;
import com.dep.biguo.utils.IdleProductStateManager;

import java.util.ArrayList;
import java.util.List;

/**
 * 闲置商品状态测试Activity
 * 用于展示修改后的商品状态管理效果
 */
public class IdleProductTestActivity extends AppCompatActivity implements View.OnClickListener {

    private IdleProductTestActivityBinding binding;
    private IdleProductAdapter adapter;
    private List<OrderBean> testData = new ArrayList<>();

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = DataBindingUtil.setContentView(this, R.layout.idle_product_test_activity);
        binding.setOnClickListener(this);
        
        setStatusBarColor();
        initViews();
        loadTestData();
    }

    private void setStatusBarColor() {
        Window window = getWindow();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            window.getDecorView().setSystemUiVisibility(
                    View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN | View.SYSTEM_UI_FLAG_LAYOUT_STABLE
            );
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
            window.setStatusBarColor(Color.TRANSPARENT);
        }
    }

    private void initViews() {
        // 设置标题
        binding.tvTitle.setText("闲置商品状态测试");
        
        // 初始化RecyclerView
        LinearLayoutManager layoutManager = new LinearLayoutManager(this);
        binding.recyclerView.setLayoutManager(layoutManager);
        
        adapter = new IdleProductAdapter(testData);
        binding.recyclerView.setAdapter(adapter);
    }

    private void loadTestData() {
        testData.clear();
        
        // 创建测试数据 - 三种不同状态的商品，都在"在卖"区域
        
        // 状态1：显示"新书上架"标签和"下架"按钮
        OrderBean item1 = IdleProductStateManager.createTestData(
            "马克思原理二", "16.00", IdleProductStateManager.STATE_ON_SALE_NEW_BOOK);
        testData.add(item1);
        
        // 状态2：显示"上架"和"编辑"按钮
        OrderBean item2 = IdleProductStateManager.createTestData(
            "毛泽东思想和中国特色社会主义理论体系概论", "30.00", IdleProductStateManager.STATE_ON_SALE_WITH_SHELF);
        testData.add(item2);
        
        // 状态3：只显示"编辑"按钮
        OrderBean item3 = IdleProductStateManager.createTestData(
            "英语二", "30.00", IdleProductStateManager.STATE_ON_SALE_EDIT_ONLY);
        testData.add(item3);
        
        // 刷新适配器
        adapter.notifyDataSetChanged();
        
        // 显示提示信息
        Toast.makeText(this, "已加载3个测试商品，都在'在卖'区域", Toast.LENGTH_LONG).show();
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.ivBack) {
            finish();
        } else if (id == R.id.btnRefresh) {
            // 重新加载测试数据
            loadTestData();
            Toast.makeText(this, "数据已刷新", Toast.LENGTH_SHORT).show();
        }
    }
}
