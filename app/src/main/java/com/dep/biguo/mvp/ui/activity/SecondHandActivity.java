package com.dep.biguo.mvp.ui.activity;

import android.content.Intent;
import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.GridLayoutManager;

import com.dep.biguo.R;
import com.dep.biguo.bean.ShopBean;
import com.dep.biguo.databinding.SecondHandActivityBinding;
import com.dep.biguo.mvp.ui.adapter.MallProductAdapter;
import com.dep.biguo.widget.ItemDecoration;

import java.util.ArrayList;
import java.util.List;

public class SecondHandActivity extends AppCompatActivity implements View.OnClickListener {

    private SecondHandActivityBinding binding;
    private MallProductAdapter adapter;
    private List<ShopBean> originalData = new ArrayList<>();
    private List<ShopBean> filteredData = new ArrayList<>();

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = DataBindingUtil.setContentView(this, R.layout.second_hand_activity);
        binding.setOnClickListener(this);

        setStatusBarColorGradientStart();
        initRecyclerView();
        initSearchListener();
        loadMockData();
    }


    private void setStatusBarColorGradientStart() {
        Window window = getWindow();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            window.getDecorView().setSystemUiVisibility(
                    View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN | View.SYSTEM_UI_FLAG_LAYOUT_STABLE
            );
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
            window.setStatusBarColor(Color.TRANSPARENT);
        }
    }
    private void initRecyclerView() {
        adapter = new MallProductAdapter(new ArrayList<>());
        GridLayoutManager gridLayoutManager = new GridLayoutManager(this, 2);
        binding.rvBooks.setLayoutManager(gridLayoutManager);
        
        // 添加间距装饰器
        int space = getResources().getDimensionPixelOffset(R.dimen.dp_8);
        binding.rvBooks.addItemDecoration(new ItemDecoration(space));
        binding.rvBooks.setAdapter(adapter);
        
        // 设置点击事件
        adapter.setOnItemClickListener((adapter, view, position) -> {
            ShopBean item = this.adapter.getItem(position);
            if (item != null) {
                // TODO: 打开商品详情页
                Toast.makeText(this, "查看商品: " + item.getName(), Toast.LENGTH_SHORT).show();
            }
        });
    }

    private void initSearchListener() {
        binding.etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {}

            @Override
            public void afterTextChanged(Editable s) {
                filterData();
            }
        });
    }

    private void loadMockData() {
        originalData.clear();
        
        // 模拟二手教材数据
        String[] bookNames = {
            "高等数学 第七版 上册", "大学英语综合教程1", "计算机基础教程",
            "线性代数与解析几何", "概率论与数理统计", "Java程序设计",
            "数据结构与算法", "操作系统原理", "数据库系统概论",
            "软件工程导论", "网络技术基础", "电路分析基础"
        };
        
        String[] publishers = {
            "高等教育出版社", "外语教学与研究出版社", "清华大学出版社",
            "人民邮电出版社", "机械工业出版社", "电子工业出版社"
        };
        
        String[] conditions = {"九成新", "八成新", "七成新", "全新"};
        String[] locations = {"昆明", "大理", "曲靖", "玉溪", "红河"};
        String[] times = {"2小时前", "5小时前", "1天前", "2天前", "3天前"};
        
        for (int i = 0; i < 20; i++) {
            ShopBean bean = new ShopBean();
            bean.setId(i + 1);
            bean.setName(bookNames[i % bookNames.length]);
            bean.setCode("BOOK" + String.format("%03d", i + 1));
            
            // 设置价格
            int originalPrice = 30 + (i % 5) * 10;
            int currentPrice = originalPrice - (5 + i % 15);
            bean.setPrice(String.valueOf(currentPrice));
            bean.setPreferential_price(String.valueOf(originalPrice));
            
            // 设置其他属性
            bean.setPublisher(publishers[i % publishers.length]);
            bean.setCondition(conditions[i % conditions.length]);
            bean.setLocation(locations[i % locations.length]);
            bean.setPublishTime(times[i % times.length]);
            bean.setUrgent(i % 7 == 0); // 部分标记为急售
            
            // 设置图片
            bean.setImg("https://picsum.photos/300/400?random=" + i);
            
            originalData.add(bean);
        }
        
        // 初始显示所有数据
        filteredData.clear();
        filteredData.addAll(originalData);
        updateUI();
    }

    private void filterData() {
        String searchText = binding.etSearch.getText().toString().trim().toLowerCase();
        
        filteredData.clear();
        for (ShopBean item : originalData) {
            boolean matchSearch = searchText.isEmpty() ||
                item.getName().toLowerCase().contains(searchText) ||
                item.getCode().toLowerCase().contains(searchText);
            
            if (matchSearch) {
                filteredData.add(item);
            }
        }
        
        updateUI();
    }

    private void updateUI() {
        adapter.setNewData(filteredData);
        
        // 显示/隐藏空状态
        binding.layoutEmpty.setVisibility(filteredData.isEmpty() ? View.VISIBLE : View.GONE);
        binding.rvBooks.setVisibility(filteredData.isEmpty() ? View.GONE : View.VISIBLE);
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.ivBack) {
            finish();
        } else if (id == R.id.btnSearch) {
            // 手动触发搜索
            filterData();
            // 隐藏键盘
            binding.etSearch.clearFocus();
        } else if (id == R.id.btnFloatingAction) {
            // 跳转到发布旧书页面
            Intent intent = new Intent(this, PublishBookActivity.class);
            startActivity(intent);
        } else if (id == R.id.btnTestStates) {
            // 跳转到闲置商品状态测试页面
            Intent intent = new Intent(this, IdleProductTestActivity.class);
            startActivity(intent);
        }
    }
} 