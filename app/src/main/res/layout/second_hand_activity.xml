<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <data>
        <variable name="onClickListener" type="com.dep.biguo.mvp.ui.activity.SecondHandActivity"/>
    </data>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/bg_gray">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <!-- 顶部栏和搜索区域 -->
            <RelativeLayout
                android:id="@+id/header"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_gradient_red"
                android:paddingStart="16dp"
                android:paddingEnd="16dp"
                android:paddingTop="49dp"
                android:paddingBottom="20dp">

                <!-- 返回按钮 -->
                <ImageView
                    android:id="@+id/ivBack"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_alignParentStart="true"
                    android:layout_alignParentTop="true"
                    android:src="@drawable/arrow_back"
                    android:onClick="@{onClickListener.onClick}"
                    android:padding="2dp"
                    android:background="?android:attr/selectableItemBackgroundBorderless"
                    app:tint="@color/white" />

                <!-- 标题 -->
                <TextView
                    android:id="@+id/tvTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentTop="true"
                    android:layout_marginStart="5dp"
                    android:layout_toEndOf="@+id/ivBack"
                    android:text="二手教材"
                    android:textColor="@color/white"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <!-- 搜索框 -->
                <RelativeLayout
                    android:id="@+id/searchBar"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_below="@id/tvTitle"
                    android:layout_marginTop="16dp"
                    android:background="@drawable/bg_search_bar">

                    <EditText
                        android:id="@+id/etSearch"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_toStartOf="@id/btnSearch"
                        android:paddingStart="16dp"
                        android:paddingEnd="8dp"
                        android:hint="二手2025专升本教材"
                        android:background="@null"
                        android:textColor="@color/tblack"
                        android:textSize="14sp"
                        android:textColorHint="@color/tblack3"
                        android:imeOptions="actionSearch"
                        android:singleLine="true" />

                    <ImageView
                        android:id="@+id/btnSearch"
                        android:layout_width="60dp"
                        android:layout_height="36dp"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="2dp"
                        android:background="@drawable/bg_capsule_yellow"
                        android:padding="10dp"
                        android:src="@drawable/icon_search"
                        android:onClick="@{onClickListener.onClick}"
                        app:tint="#000000" />
                </RelativeLayout>

            </RelativeLayout>

            <!-- 商品列表 -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvBooks"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:paddingStart="16dp"
                android:paddingEnd="16dp"
                android:paddingTop="16dp"
                android:paddingBottom="80dp"
                android:clipToPadding="false"
                app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                app:spanCount="2" />

            <!-- 空状态视图 -->
            <LinearLayout
                android:id="@+id/layoutEmpty"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:orientation="vertical"
                android:gravity="center"
                android:visibility="gone">

                <ImageView
                    android:layout_width="120dp"
                    android:layout_height="120dp"
                    android:src="@drawable/goods_detail_right_time"
                    android:alpha="0.6" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:text="暂无二手教材"
                    android:textColor="@color/tblack3"
                    android:textSize="16sp" />

            </LinearLayout>

        </LinearLayout>

        <!-- 悬浮按钮组 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom|center_horizontal"
            android:layout_marginBottom="20dp"
            android:orientation="horizontal"
            android:gravity="center">

            <TextView
                android:id="@+id/btnFloatingAction"
                android:layout_width="80dp"
                android:layout_height="40dp"
                android:background="@drawable/bg_gradient_green"
                android:gravity="center"
                android:text="卖闲置"
                android:textColor="@color/white"
                android:textSize="14sp"
                android:textStyle="bold"
                android:onClick="@{onClickListener.onClick}" />

            <TextView
                android:id="@+id/btnTestStates"
                android:layout_width="80dp"
                android:layout_height="40dp"
                android:layout_marginStart="10dp"
                android:background="@drawable/bg_gradient_red"
                android:gravity="center"
                android:text="测试状态"
                android:textColor="@color/white"
                android:textSize="12sp"
                android:textStyle="bold"
                android:onClick="@{onClickListener.onClick}" />

        </LinearLayout>

    </FrameLayout>
</layout>