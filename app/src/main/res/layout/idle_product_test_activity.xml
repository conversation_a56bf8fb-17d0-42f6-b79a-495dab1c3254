<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <variable
            name="onClickListener"
            type="android.view.View.OnClickListener" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:background="@color/white">

        <!-- 标题栏 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:background="@color/white"
            android:paddingStart="16dp"
            android:paddingEnd="16dp">

            <ImageView
                android:id="@+id/ivBack"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/arrow_white_left"
                android:onClick="@{onClickListener}"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:padding="4dp"
                android:tint="@color/tblack" />

            <TextView
                android:id="@+id/tvTitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="闲置商品状态测试"
                android:textColor="@color/tblack"
                android:textSize="18sp"
                android:textStyle="bold"
                android:gravity="center"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp" />

            <TextView
                android:id="@+id/btnRefresh"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="刷新"
                android:textColor="@color/theme"
                android:textSize="14sp"
                android:onClick="@{onClickListener}"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:padding="8dp" />

        </LinearLayout>

        <!-- 分割线 -->
        <View
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:background="#EBEBEB" />

        <!-- 说明文字 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="以下三个商品都在'在卖'区域，展示不同的状态样式："
            android:textColor="@color/tblack2"
            android:textSize="14sp"
            android:padding="16dp"
            android:background="#F8F8F8" />

        <!-- 商品列表 -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:background="@color/bg_gray"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

    </LinearLayout>
</layout>
